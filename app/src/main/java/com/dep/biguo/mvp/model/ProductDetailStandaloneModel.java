package com.dep.biguo.mvp.model;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.mvp.contract.ProductDetailStandaloneContract;
import com.dep.biguo.mvp.model.api.service.GroupService;
import com.google.gson.Gson;
import com.jess.arms.integration.IRepositoryManager;
import com.jess.arms.mvp.BaseModel;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;

public class ProductDetailStandaloneModel extends BaseModel implements ProductDetailStandaloneContract.Model {
    @Inject Gson mGson;
    @Inject Application mApplication;

    @Inject
    public ProductDetailStandaloneModel(IRepositoryManager repositoryManager) {
        super(repositoryManager);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mGson = null;
        this.mApplication = null;
    }

    @Override
    public Observable<BaseResponse<List<GroupCommentBean>>> getComments(int goodsId, int page, int limit) {
        // Reuse comments API: type=book, value=goods_id
        return Observable.just(mRepositoryManager.obtainRetrofitService(GroupService.class)
                .getCommentList(0, 0, com.dep.biguo.utils.pay.PayUtils.BOOK, String.valueOf(goodsId), 0, page, limit))
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }
}

