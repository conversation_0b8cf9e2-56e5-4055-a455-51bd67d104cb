package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dep.biguo.R;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.databinding.ProductDetailStandaloneFragmentBinding;
import com.dep.biguo.di.component.DaggerProductDetailStandaloneFragComponent;
import com.dep.biguo.mvp.ui.adapter.GroupCommentAdapter;
import com.dep.biguo.mvp.presenter.ProductDetailStandaloneFragPresenter;
import com.dep.biguo.mvp.contract.ProductDetailStandaloneContract;
import com.dep.biguo.utils.html.HtmlUtil;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import android.widget.TextView;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;
import com.dep.biguo.utils.BannerRoundImageLoader;
import java.util.ArrayList;
import java.util.Arrays;

import java.util.List;

public class ProductDetailStandaloneFragment extends BaseFragment<ProductDetailStandaloneFragPresenter> implements ProductDetailStandaloneContract.View {
    private ProductDetailStandaloneFragmentBinding binding;
    private GroupCommentAdapter commentAdapter;
    private int goodsId;
    private int initCommentCount;

    public static ProductDetailStandaloneFragment create(int goodsId, int initCommentCount, String title, String html){
        Bundle b = new Bundle();
        b.putInt("goodsId", goodsId);
        b.putInt("initCount", initCommentCount);
        b.putString("title", title);
        b.putString("html", html);
        ProductDetailStandaloneFragment f = new ProductDetailStandaloneFragment();
        f.setArguments(b);
        return f;
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerProductDetailStandaloneFragComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.product_detail_standalone_fragment, container, false);
        binding = DataBindingUtil.bind(view);
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        goodsId = getArguments() == null ? 0 : getArguments().getInt("goodsId");
        initCommentCount = getArguments() == null ? 0 : getArguments().getInt("initCount");
        String title = getArguments() == null ? null : getArguments().getString("title");
        String html = getArguments() == null ? null : getArguments().getString("html");

        setupHeader(title);
        commentAdapter = new GroupCommentAdapter(new ArrayList<>());
        binding.commentRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        commentAdapter.bindToRecyclerView(binding.commentRecyclerView);

        HtmlUtil.form(html == null ? "" : html, getResources().getDisplayMetrics().widthPixels)
                .setTargetView(binding.introduceView);

        setCommentCountTitle(initCommentCount);

        // 全部评价点击
        binding.allCommentView.setOnClickListener(v ->
                com.dep.biguo.mvp.ui.activity.GroupCommentActivity.start(
                        getActivity(), 0, String.valueOf(goodsId), 0,
                        com.dep.biguo.utils.pay.PayUtils.BOOK
                )
        );

        if (mPresenter != null) mPresenter.loadComments(1, 2);
    }

    @Override
    public void setData(@Nullable Object data) {
        // no-op
    }

    @Override
    public void showComments(List<GroupCommentBean> comments) {
        commentAdapter.setNewData(comments);
        // 控制“全部”可见性
        boolean showAll = (comments != null && comments.size() > 2) || initCommentCount > 2;
        binding.allCommentView.setVisibility(showAll ? View.VISIBLE : View.GONE);
    }

    @Override
    public void setCommentCountTitle(int count) {
        binding.commentTitleView.setText("买家评价 (" + count + ")");
    }

    @Override
    public int getInitialCommentCount() { return initCommentCount; }

    private void setupHeader(String title){
        // 标题
        TextView headerTitle = binding.getRoot().findViewById(R.id.headerTitleView);
        if(headerTitle != null){
            headerTitle.setText(title == null ? "商品名称" : title);
        }
        // 轮播（此 Fragment 版本仅演示，实际图片由外部传入可再扩展）
        Banner banner = binding.getRoot().findViewById(R.id.bannerView);
        if(banner != null){
            ArrayList<String> images = new ArrayList<>(Arrays.asList(""));
            banner.setImages(images);
            banner.setImageLoader(new BannerRoundImageLoader());
            banner.setBannerStyle(BannerConfig.NOT_INDICATOR);
            banner.start();
        }
    }

    public int getGoodsId() { return goodsId; }

    @Override
    public void showMessage(@NonNull String message) {
        com.hjq.toast.ToastUtils.show(message);
    }
}
