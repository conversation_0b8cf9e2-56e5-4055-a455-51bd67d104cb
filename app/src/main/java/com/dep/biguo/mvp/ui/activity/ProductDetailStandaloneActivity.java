package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.View;

import com.dep.biguo.R;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.databinding.ProductDetailStandaloneActivityBinding;
import com.dep.biguo.di.component.DaggerProductDetailStandaloneComponent;
import com.dep.biguo.mvp.contract.ProductDetailStandaloneContract;
import com.dep.biguo.mvp.presenter.ProductDetailStandalonePresenter;
import com.dep.biguo.mvp.ui.adapter.GroupCommentAdapter;
import com.dep.biguo.utils.html.HtmlUtil;
import android.widget.TextView;
import android.widget.ImageView;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.AppUtil;
import java.util.Arrays;
import com.dep.biguo.utils.pay.PayUtils;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductDetailStandaloneActivity extends BaseLoadSirActivity<ProductDetailStandalonePresenter> implements ProductDetailStandaloneContract.View, View.OnClickListener {
    public static final String EXTRA_GOODS_ID = "extra_goods_id";
    public static final String EXTRA_TITLE = "extra_title";
    public static final String EXTRA_INTRO_HTML = "extra_intro_html";
    public static final String EXTRA_COMMENT_COUNT = "extra_comment_count";


    // 可选扩展字段（若未传则使用默认占位）
    public static final String EXTRA_PRICE = "extra_price"; // e.g. "¥2500.00"
    public static final String EXTRA_RATING = "extra_rating"; // e.g. "4.8"
    public static final String EXTRA_RATING_COUNT = "extra_rating_count"; // e.g. "231"
    public static final String EXTRA_BRIEF = "extra_brief"; // 简短描述
    public static final String EXTRA_IMAGE_URLS = "extra_image_urls"; // ArrayList<String>

    private ProductDetailStandaloneActivityBinding binding;
    private int goodsId;
    private int initCommentCount;

    private GroupCommentAdapter commentAdapter;

    public static void start(Context context, int goodsId, String title, String introHtml, int commentCount){
        Intent intent = new Intent(context, ProductDetailStandaloneActivity.class);
        intent.putExtra(EXTRA_GOODS_ID, goodsId);
        intent.putExtra(EXTRA_TITLE, title);
        intent.putExtra(EXTRA_INTRO_HTML, introHtml);
        intent.putExtra(EXTRA_COMMENT_COUNT, commentCount);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerProductDetailStandaloneComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.product_detail_standalone_activity);
        binding.setOnClickListener(this);
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        goodsId = getIntent().getIntExtra(EXTRA_GOODS_ID, 0);
        initCommentCount = getIntent().getIntExtra(EXTRA_COMMENT_COUNT, 0);
        String title = getIntent().getStringExtra(EXTRA_TITLE);
        String html = getIntent().getStringExtra(EXTRA_INTRO_HTML);

        // 头部：轮播/价格/评分/标题/描述
        setupHeader(title);

        // 评论列表
        commentAdapter = new GroupCommentAdapter(new ArrayList<>());
        binding.commentRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        commentAdapter.bindToRecyclerView(binding.commentRecyclerView);

        // 详情介绍
        HtmlUtil.form(html == null ? "" : html, getResources().getDisplayMetrics().widthPixels)
                .setTargetView(binding.introduceView);

        // 初始标题计数
        setCommentCountTitle(initCommentCount);

        onRequest();
    }

    @Override
    public void onRequest() {
        mPresenter.loadComments(1, 2); // 预览只拉取两条
    }

    @Override
    public void onClick(View v) {
        if (v == binding.allCommentView) {
            GroupCommentActivity.start(this, 0, String.valueOf(goodsId), 0, PayUtils.BOOK);
        }
    }

    @Override
    public void showComments(List<GroupCommentBean> comments) {
        commentAdapter.setNewData(comments);
        binding.allCommentView.setVisibility((comments == null || comments.size() <= 2) && initCommentCount <= 2 ? View.GONE : View.VISIBLE);
    }


    private void setupHeader(String title){
        // 标题
        TextView headerTitle = findViewById(R.id.headerTitleView);
        if(headerTitle != null){
            headerTitle.setText(title == null ? "商品名称" : title);
        }

        // 价格
        TextView priceView = findViewById(R.id.priceView);
        String price = getIntent().getStringExtra(EXTRA_PRICE);
        if(priceView != null){
            priceView.setText(AppUtil.isEmpty(price, "¥2500.00"));
        }

        // 评分与评价数
        TextView ratingTextView = findViewById(R.id.ratingTextView);
        String rating = getIntent().getStringExtra(EXTRA_RATING);
        String ratingCount = getIntent().getStringExtra(EXTRA_RATING_COUNT);
        if(ratingTextView != null){
            String show = (AppUtil.isEmpty(rating) ? "4.8" : rating) + " (" + (AppUtil.isEmpty(ratingCount) ? "0" : ratingCount) + ")";
            ratingTextView.setText(show);
        }

        // 简述
        TextView briefView = findViewById(R.id.briefView);
        String brief = getIntent().getStringExtra(EXTRA_BRIEF);
        if(briefView != null){
            briefView.setText(AppUtil.isEmpty(brief, "这是对商品的描述。这是对商品的描述这是对商品的描述。"));
        }

        // 轮播
        Banner banner = findViewById(R.id.bannerView);
        if(banner != null){
            ArrayList<String> images = getIntent().getStringArrayListExtra(EXTRA_IMAGE_URLS);
            if(images == null) images = new ArrayList<>(Arrays.asList(""));
            banner.setImages(images);
            banner.setImageLoader(new BannerRoundImageLoader());
            banner.setBannerStyle(BannerConfig.NOT_INDICATOR);
            banner.start();

            TextView indexView = findViewById(R.id.bannerIndexView);
            ArrayList<String> finalImages = images;
            banner.setOnBannerListener(position -> {
                if(indexView != null){
                    indexView.setText((position + 1) + "/" + (finalImages == null ? 1 : finalImages.size()));
                }
            });
            if(indexView != null){
                indexView.setText("1/" + (images == null ? 1 : images.size()));
            }
        }
    }

    @Override
    public void setCommentCountTitle(int count) {
        binding.commentTitleView.setText("买家评价 (" + count + ")");
    }

    @Override
    public int getInitialCommentCount() { return initCommentCount; }

    @Override
    public int getGoodsId() { return goodsId; }

    @Override
    public Activity getActivity() { return this; }

    @Override
    public void showMessage(@NonNull String message) { ArmsUtils.snackbarText(message); }
}

