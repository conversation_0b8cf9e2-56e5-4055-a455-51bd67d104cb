<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data/>

    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 顶部图片轮播 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:background="@color/white">

            <com.youth.banner.Banner
                android:id="@+id/bannerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/img_banner_placeholder"/>

            <!-- 底部右侧张数指示 -->
            <TextView
                android:id="@+id/bannerIndexView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="10dp"
                android:background="@drawable/bg_round_5_bgc"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="3dp"
                android:paddingBottom="3dp"
                android:text="1/1"
                android:textColor="@color/tblack2"
                android:textSize="10sp"/>
        </FrameLayout>

        <!-- 基础信息卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/white"
            android:paddingStart="@dimen/screenSpace"
            android:paddingEnd="@dimen/screenSpace"
            android:paddingTop="10dp"
            android:paddingBottom="12dp">

            <!-- 价格 + 评分 行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.dep.biguo.widget.DiversificationTextView
                    android:id="@+id/priceView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥2500.00"
                    android:textColor="@color/theme"
                    android:textSize="16dp"
                    app:startChar="¥"
                    app:size="36dp"/>

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"/>

                <LinearLayout
                    android:id="@+id/ratingLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">
                    <ImageView
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:tint="#FFA000"
                        android:src="@drawable/ic_fire"/>
                    <TextView
                        android:id="@+id/ratingTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="4.8 (0)"
                        android:textColor="@color/tblack2"
                        android:textSize="12sp"/>
                </LinearLayout>
            </LinearLayout>

            <!-- 标题 -->
            <TextView
                android:id="@+id/headerTitleView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:text="商品名称"
                android:textColor="@color/tblack"
                android:textSize="18dp"/>

            <!-- 简短描述 -->
            <TextView
                android:id="@+id/briefView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:lineSpacingExtra="2dp"
                android:text="这是对商品的描述。这是对商品的描述这是对商品的描述。"
                android:textColor="@color/tblack3"
                android:textSize="12sp"/>

            <!-- 热卖榜 标签 -->
            <TextView
                android:id="@+id/hotRankTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/bg_sales_rank_tag"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="3dp"
                android:paddingBottom="3dp"
                android:text="当前学习工具热卖榜·TOP3"
                android:textColor="@color/white"
                android:textSize="10sp"/>
        </LinearLayout>

    </LinearLayout>
</layout>

