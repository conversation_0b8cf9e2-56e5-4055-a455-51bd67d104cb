<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ProductDetailStandaloneActivity" />
    </data>
    <androidx.core.widget.NestedScrollView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgc">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 商品头部区域（轮播、价格、评分、标题、描述、热卖榜标签） -->
            <include
                android:id="@+id/header"
                layout="@layout/product_detail_header" />

            <!-- 评价模块：复用 group_goods_info_fragment 的结构风格 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingStart="@dimen/screenSpace"
                android:paddingEnd="@dimen/screenSpace"
                android:layout_marginTop="10dp">

                <TextView
                    android:id="@+id/commentTitleView"
                    android:text="买家评价 (0)"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <TextView
                    android:id="@+id/allCommentView"
                    android:text="全部"
                    android:textSize="12dp"
                    android:textColor="@color/tblack2"
                    android:drawableEnd="@drawable/arrow_right"
                    android:onClick="@{onClickListener::onClick}"
                    android:drawablePadding="2dp"
                    android:paddingStart="8dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="0dp"
                    android:paddingBottom="8dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="@id/commentTitleView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="@id/commentTitleView"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/commentRecyclerView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:minHeight="10dp"
                    android:nestedScrollingEnabled="false"
                    android:layout_marginTop="4dp"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_rv_group_comment"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/commentTitleView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 详情介绍 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingStart="@dimen/screenSpace"
                android:paddingEnd="@dimen/screenSpace"
                android:paddingBottom="10dp"
                android:layout_marginTop="10dp">

                <TextView
                    android:text="详情介绍"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"/>

                <TextView
                    android:id="@+id/introduceView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:nestedScrollingEnabled="false"/>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>

